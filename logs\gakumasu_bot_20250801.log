2025-08-01 09:43:28 - GakumasuBot - INFO - logger.py:109 - 日志系统初始化完成 - 级别: INFO
2025-08-01 09:43:28 - GakumasuBot - INFO - gui.py:76 - GUI启动器初始化完成
2025-08-01 09:43:28 - GakumasuBot - INFO - gui.py:235 - 启动模式: development
2025-08-01 09:43:28 - GakumasuBot - INFO - gui.py:106 - 后端端口 8000 可用
2025-08-01 09:43:28 - GakumasuBot - INFO - gui.py:116 - 前端端口 3000 可用
2025-08-01 09:43:28 - GakumasuBot - INFO - gui.py:130 - 启动后端服务: uvicorn src.web.main:app --host 127.0.0.1 --port 8000 --reload
2025-08-01 09:43:28 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:8000/health
2025-08-01 09:43:28 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Will watch for changes in these directories: ['J:\\笔记本专用\\代码\\Gakumasu-Bot']
2025-08-01 09:43:28 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
2025-08-01 09:43:28 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started reloader process [33948] using StatReload
2025-08-01 09:43:32 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started server process [9300]
2025-08-01 09:43:32 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Waiting for application startup.
2025-08-01 09:43:32 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Application startup complete.
2025-08-01 09:43:32 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52936 - "GET /health HTTP/1.1" 200 OK
2025-08-01 09:43:32 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:8000/health
2025-08-01 09:43:32 - GakumasuBot - INFO - gui.py:166 - 启动前端服务: npm run dev
2025-08-01 09:43:32 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:3000
2025-08-01 09:43:34 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:3000
2025-08-01 09:43:34 - GakumasuBot - INFO - gui.py:260 - 所有服务启动完成，按 Ctrl+C 退出
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52988 - "WebSocket /ws" [accepted]
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: 未找到游戏窗口: gakumas
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52990 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52992 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52999 - "GET /screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53000 - "GET /screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53002 - "GET /screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53001 - "GET /screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53012 - "GET /screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53011 - "GET /screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53026 - "GET /screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53025 - "GET /screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53023 - "GET /screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53024 - "GET /screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53032 - "GET /screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53034 - "GET /screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53081 - "WebSocket /ws" [accepted]
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53083 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: 未找到游戏窗口: gakumas
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53085 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53097 - "WebSocket /ws" [accepted]
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53099 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: 未找到游戏窗口: gakumas
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53101 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败:
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55508 - "WebSocket /ws" [accepted]
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55510 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55512 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 09:57:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55722 - "WebSocket /ws" [accepted]
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55726 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55725 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55742 - "WebSocket /ws" [accepted]
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55744 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55746 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:18 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55902 - "GET /api/v1/screenshot/preview HTTP/1.1" 200 OK
2025-08-01 09:59:18 - GakumasuBot - INFO - gui.py:146 - Backend: WebSocket连接错误: WebSocket is not connected. Need to call "accept" first.
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55924 - "WebSocket /ws" [accepted]
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55926 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55928 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 10:00:06 - GakumasuBot - INFO - gui.py:281 - 正在停止所有服务...
2025-08-01 10:00:06 - GakumasuBot - INFO - gui.py:289 - 前端服务已停止
2025-08-01 10:00:06 - GakumasuBot - INFO - gui.py:301 - 后端服务已停止
2025-08-01 10:00:06 - GakumasuBot - INFO - gui.py:308 - 所有服务已停止
2025-08-01 10:06:16 - GakumasuBot - INFO - logger.py:109 - 日志系统初始化完成 - 级别: INFO
2025-08-01 10:06:16 - GakumasuBot - INFO - gui.py:76 - GUI启动器初始化完成
2025-08-01 10:06:16 - GakumasuBot - INFO - gui.py:235 - 启动模式: development
2025-08-01 10:06:16 - GakumasuBot - INFO - gui.py:106 - 后端端口 8000 可用
2025-08-01 10:06:16 - GakumasuBot - INFO - gui.py:116 - 前端端口 3000 可用
2025-08-01 10:06:16 - GakumasuBot - INFO - gui.py:130 - 启动后端服务: uvicorn src.web.main:app --host 127.0.0.1 --port 8000 --reload
2025-08-01 10:06:16 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:8000/health
2025-08-01 10:06:16 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Will watch for changes in these directories: ['J:\\笔记本专用\\代码\\Gakumasu-Bot']
2025-08-01 10:06:16 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
2025-08-01 10:06:16 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started reloader process [18488] using StatReload
2025-08-01 10:06:19 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started server process [21836]
2025-08-01 10:06:19 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Waiting for application startup.
2025-08-01 10:06:19 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Application startup complete.
2025-08-01 10:06:20 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:57126 - "GET /health HTTP/1.1" 200 OK
2025-08-01 10:06:20 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:8000/health
2025-08-01 10:06:20 - GakumasuBot - INFO - gui.py:166 - 启动前端服务: npm run dev
2025-08-01 10:06:20 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:3000
2025-08-01 10:06:22 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:3000
2025-08-01 10:06:22 - GakumasuBot - INFO - gui.py:260 - 所有服务启动完成，按 Ctrl+C 退出
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:57155 - "WebSocket /ws" [accepted]
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:57157 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:57159 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 10:07:36 - GakumasuBot - INFO - gui.py:281 - 正在停止所有服务...
2025-08-01 10:07:36 - GakumasuBot - INFO - gui.py:289 - 前端服务已停止
2025-08-01 10:07:36 - GakumasuBot - INFO - gui.py:301 - 后端服务已停止
2025-08-01 10:07:36 - GakumasuBot - INFO - gui.py:308 - 所有服务已停止
