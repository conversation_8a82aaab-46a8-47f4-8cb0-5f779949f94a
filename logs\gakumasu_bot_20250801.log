2025-08-01 09:43:28 - GakumasuBot - INFO - logger.py:109 - 日志系统初始化完成 - 级别: INFO
2025-08-01 09:43:28 - GakumasuBot - INFO - gui.py:76 - GUI启动器初始化完成
2025-08-01 09:43:28 - GakumasuBot - INFO - gui.py:235 - 启动模式: development
2025-08-01 09:43:28 - GakumasuBot - INFO - gui.py:106 - 后端端口 8000 可用
2025-08-01 09:43:28 - GakumasuBot - INFO - gui.py:116 - 前端端口 3000 可用
2025-08-01 09:43:28 - GakumasuBot - INFO - gui.py:130 - 启动后端服务: uvicorn src.web.main:app --host 127.0.0.1 --port 8000 --reload
2025-08-01 09:43:28 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:8000/health
2025-08-01 09:43:28 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Will watch for changes in these directories: ['J:\\笔记本专用\\代码\\Gakumasu-Bot']
2025-08-01 09:43:28 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
2025-08-01 09:43:28 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started reloader process [33948] using StatReload
2025-08-01 09:43:32 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started server process [9300]
2025-08-01 09:43:32 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Waiting for application startup.
2025-08-01 09:43:32 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Application startup complete.
2025-08-01 09:43:32 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52936 - "GET /health HTTP/1.1" 200 OK
2025-08-01 09:43:32 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:8000/health
2025-08-01 09:43:32 - GakumasuBot - INFO - gui.py:166 - 启动前端服务: npm run dev
2025-08-01 09:43:32 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:3000
2025-08-01 09:43:34 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:3000
2025-08-01 09:43:34 - GakumasuBot - INFO - gui.py:260 - 所有服务启动完成，按 Ctrl+C 退出
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52988 - "WebSocket /ws" [accepted]
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: 未找到游戏窗口: gakumas
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52990 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52992 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52999 - "GET /screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53000 - "GET /screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53002 - "GET /screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53001 - "GET /screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53012 - "GET /screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53011 - "GET /screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53026 - "GET /screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53025 - "GET /screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53023 - "GET /screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53024 - "GET /screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53032 - "GET /screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53034 - "GET /screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53081 - "WebSocket /ws" [accepted]
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53083 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: 未找到游戏窗口: gakumas
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53085 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53097 - "WebSocket /ws" [accepted]
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53099 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: 未找到游戏窗口: gakumas
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53101 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
